ENV = 'development'
VITE_CLI_PORT = 8080
VITE_SERVER_PORT = 8888
VITE_BASE_API = /api
VITE_FILE_API = /api
VITE_BASE_PATH = http://127.0.0.1
VITE_POSITION = close
VITE_EDITOR = vscode
// VITE_EDITOR = webstorm 如果使用webstorm开发且要使用dom定位到代码行功能 请先自定添加 webstorm到环境变量 再将VITE_EDITOR值修改为webstorm
// 如果使用docker-compose开发模式，设置为下面的地址或本机主机IP
//VITE_BASE_PATH = http://**********
# oa-oauth2.0登录客户端ID
VITE_OA_LOGIN_CLINET_ID =
# oa地址
VITE_OA_URL =
