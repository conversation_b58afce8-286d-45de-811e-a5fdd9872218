import service from '@/utils/request'
// @Summary 查询工作区列表
// @Produce  application/json
// @Param data body {name:"string",page:"number",pageSize:"number"}
export const getWorkspaceList = (data) => {
    return service({
        url: '/workspace/getWorkspaceList',
        method: 'post',
        data: data
    })
}

// @Summary 查询工作区间下的用户列表
// @Produce  application/json
// @Param data body {tenantId:"string"}
export const getUserListByTenantId = (data) => {
    return service({
        url: '/workspace/getUserListByTenantId',
        method: 'post',
        data
    })
}

// @Summary 查询所有用户列表
// @Produce  application/json
// @Param data body {name:"string"，employeeNumber:string,page:"number",pageSize:"number"}
export const getUserList = (data) => {
    return service({
        url: '/workspace/getUserList',
        method: 'post',
        data
    })
}


// 删除工作区
// @Produce  	application/x-www-form-urlencoded
// @Param data body {tenantId:"string"}
export const deleteWorkspace = (data) => {
    return service({
        url: '/workspace/deleteWorkspace',
        method: 'delete',
        data: data,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    })
}


// 新增工作区间
// @Produce  application/json
// @Param data body {name:"string"}
export const addWorkspace = (data) => {
    return service({
        url: '/workspace/addWorkspace',
        method: 'post',
        data
    })
}

// 编辑工作区间
// @Produce  application/json
// @Param data body {name:"string",tenantId:string}
export const updateWorkspace = (data) => {
    return service({
        url: '/workspace/updateWorkspace',
        method: 'post',
        data
    })
}

// 关联用户
// @Produce  application/json
// @Param data body {accountDTOList:[{id:string,role:string}],tenantId:string}
export const linkUsers = (data) => {
    return service({
        url: '/workspace/linkUsers',
        method: 'post',
        data
    })
}

