import service from '@/utils/request'
// @Summary 查询工作区列表
// @Produce  application/json
// @Param data body {name:"string",page:"number",pageSize:"number"}
export const getWorkspaceList = (data) => {
    return service({
        url: '/workspace/getWorkspaceList',
        method: 'post',
        data: data
    })
}

// @Summary 查询工作区间下的用户列表
// @Produce  application/json
// @Param data body {tenantId:"string"}
export const getUserListByTenantId = (data) => {
    return service({
        url: '/workspace/getUserListByTenantId',
        method: 'post',
        data
    })
}
