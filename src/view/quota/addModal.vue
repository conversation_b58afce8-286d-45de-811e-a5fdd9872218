<template>
  <el-dialog
    :model="addModalVisible"
    title="新增用户额度"
    width="500"
    :before-close="closeAddModal"
  >
    <div>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户" prop="accountId">
          <el-select
            v-model="form.accountId"
            placeholder="请选择用户"
            filterable
            :loading="userListLoading"
          >
            <el-option
              v-for="user in userList"
              :key="user.ID"
              :label="`${user.nickName || user.userName} (${user.userName})`"
              :value="user.ID"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="配额" prop="quota">
          <el-input
            v-model="form.quota"
            placeholder="请输入配额金额"
            type="number"
            :min="0"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeAddModal">取消</el-button>
        <el-button type="primary" @click="addQuota"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { ElMessage } from "element-plus";
import { getUserList } from "@/api/user";

// 定义 props
const props = defineProps({
  addModalVisible: {
    type: Boolean,
    default: false,
  },
});

console.log("ss", props.addModalVisible);

// 定义 emits
const emit = defineEmits(["closeAddModal", "addQuota"]);

// 表单数据
const form = reactive({
  accountId: "",
  quota: "",
});

// 表单引用
const formRef = ref(null);

// 表单验证规则
const rules = reactive({
  accountId: [{ required: true, message: "请选择用户", trigger: "change" }],
  quota: [
    { required: true, message: "请输入配额", trigger: "blur" },
    {
      pattern: /^\d+(\.\d+)?$/,
      message: "请输入有效的数字",
      trigger: "blur",
    },
    {
      validator: (rule, value, callback) => {
        if (value && Number(value) <= 0) {
          callback(new Error("配额必须大于0"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
});

// 用户列表相关
const userList = ref([]);
const userListLoading = ref(false);

// 获取用户列表
const getUserListData = async () => {
  userListLoading.value = true;
  try {
    const res = await getUserList({
      page: 1,
      pageSize: 1000, // 获取所有用户
    });
    if (res.code === 0) {
      userList.value = res.data.list || [];
    }
  } catch (error) {
    ElMessage.error("获取用户列表失败");
  } finally {
    userListLoading.value = false;
  }
};

// 监听弹窗显示状态，当弹窗打开时获取用户列表
watch(
  () => props.addModalVisible,
  (newVal) => {
    if (newVal) {
      getUserListData();
    }
  }
);

// 关闭弹窗
const closeAddModal = () => {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.accountId = "";
  form.quota = "";
  emit("closeAddModal");
};

// 添加配额
const addQuota = () => {
  if (!formRef.value) return;

  formRef.value.validate((valid) => {
    if (valid) {
      // 表单验证通过，提交数据
      emit("addQuota", { ...form });
      closeAddModal();
    } else {
      ElMessage.error("请正确填写表单信息");
    }
  });
};
</script>
