<template>
  <el-dialog
    v-model="addModalVisible"
    title="新增用户额度"
    width="500"
    :before-close="closeAddModal"
  >
    <div>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="用户">
          <el-select v-model="form.accountId" placeholder="请选择用户">
            <el-option label="Zone one" value="shanghai" />
            <el-option label="Zone two" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="配额">
          <el-input v-model="form.quota" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeAddModal">取消</el-button>
        <el-button type="primary" @click="addQuota"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { ElMessageBox } from "element-plus";

const dialogVisible = ref(false);

defineProps({
  addModalVisible: {
    type: String,
    default: "false",
  },
  closeAddModal: {
    type: Function,
    default: () => {},
  },
  addQuota: {
    type: Function,
    default: () => {},
  },
});
</script>
