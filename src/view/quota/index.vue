<template>
  <div>
    <div class="gva-search-box">
      <el-form
        ref="searchForm"
        :inline="true"
        :model="searchInfo"
        class="form-inline"
      >
        <el-form-item label="姓名">
          <el-input
            v-model="searchInfo.name"
            placeholder="请输入姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="工号">
          <el-input
            v-model="searchInfo.employeeNumber"
            placeholder="请输入工号"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">
            查询
          </el-button>
          <el-button icon="refresh" @click="onReset"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <!-- <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openQuotaModal">
          新增
        </el-button>
      </div> -->
      <el-table :data="tableData" row-key="accountId">
        <el-table-column align="left" label="头像" min-width="75">
          <template #default="scope">
            <CustomPic style="margin-top: 8px" :pic-src="scope.row.avatar" />
          </template>
        </el-table-column>
        <el-table-column align="left" label="排名" min-width="50" prop="rank" />
        <el-table-column
          align="left"
          label="姓名（工号）"
          min-width="150"
          prop="fullname"
        >
          <template #default="scope">
            <span>{{ scope.row.fullname ?? "-" }}</span>
            <span v-show="scope.row.employeeNumber"
              >({{ scope.row.employeeNumber }})</span
            >
          </template>
        </el-table-column>
        <el-table-column
          align="left"
          label="已使用配额"
          min-width="150"
          prop="usedQuota"
        />
        <el-table-column
          align="left"
          label="配额"
          min-width="180"
          prop="totalQuota"
        />
        <el-table-column align="left" label="余额" min-width="180" prop="rest">
        </el-table-column>
        <el-table-column label="操作" min-width="250" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="edit"
              @click="editQuotaFunc(scope.row, 'edit')"
              >修改额度</el-button
            >
            <el-button
              v-show="scope.row.isAvailable === true"
              type="danger"
              link
              icon="circle-close"
              @click="stopQuotaFunc(scope.row)"
              >停用</el-button
            >
            <el-button
              v-show="scope.row.isAvailable === false"
              type="success"
              link
              icon="connection"
              @click="editQuotaFunc(scope.row, 'start')"
              >启用</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { setQuota, getMoneyList } from "@/api/user";

import { getAuthorityList } from "@/api/authority";
import CustomPic from "@/components/customPic/index.vue";

import { ref, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

defineOptions({
  name: "QuotaList",
});

const searchInfo = ref({
  name: "",
  employeeNumber: "",
});

//查询
const onSubmit = () => {
  page.value = 1;
  getTableData();
};

//重置
const onReset = () => {
  searchInfo.value = {};
  page.value = 1;
  getTableData();
};

// 初始化相关
// const setAuthorityOptions = (AuthorityData, optionsData) => {
//   AuthorityData &&
//     AuthorityData.forEach((item) => {
//       if (item.children && item.children.length) {
//         const option = {
//           authorityId: item.authorityId,
//           authorityName: item.authorityName,
//           children: [],
//         };
//         setAuthorityOptions(item.children, option.children);
//         optionsData.push(option);
//       } else {
//         const option = {
//           authorityId: item.authorityId,
//           authorityName: item.authorityName,
//         };
//         optionsData.push(option);
//       }
//     });
// };

const page = ref(1);
const total = ref(0);
const pageSize = ref(10);
const tableData = ref([]);
// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  getTableData();
};

const handleCurrentChange = (val) => {
  page.value = val;
  getTableData();
};
// 查询
const getTableData = async () => {
  const table = await getMoneyList({
    page: page.value,
    pageSize: pageSize.value,
    ...searchInfo.value,
  });
  if (table.code === 0) {
    tableData.value = table.data.list;
    total.value = table.data.total;
    page.value = table.data.page;
    pageSize.value = table.data.pageSize;
  }
};

// watch(
//   () => tableData.value,
//   () => {
//     setAuthorityIds();
//   }
// );

const initPage = async () => {
  getTableData();
  // const res = await getAuthorityList();
  // setOptions(res.data);
};

initPage();

const editQuotaFunc = (row, flag) => {
  const TEXT = flag === "edit" ? "修改" : "启用";
  ElMessageBox.prompt(
    `${row.fullname ?? ""}(${row.employeeNumber ?? ""})当前的额度: ${
      row.totalQuota
    } $，${TEXT}为`,
    `${TEXT}配额`,
    {
      confirmButtonText: "修改",
      cancelButtonText: "取消",
      inputPattern: /^\d+(\.\d+)?$/,
      inputErrorMessage: "请配置用户月额度限制",
      // inputValue: row.totalQuota,
    }
  ).then(async ({ value }) => {
    const res = await setQuota({
      quota: Number(value.trim()),
      accountId: row.accountId,
      key: "update",
    });
    if (res.code === 0) {
      initPage();
      ElMessage({
        type: "success",
        message: res.msg,
      });
    } else {
      ElMessage({
        type: "error",
        message: res.msg,
      });
    }
  });
};

const stopQuotaFunc = (row) => {
  ElMessageBox.confirm(
    `确定要停用${row.fullname ?? ""}(${row.employeeNumber ?? ""})吗？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(async () => {
    const res = await setQuota({ accountId: row.accountId, key: "stop" });
    if (res.code === 0) {
      initPage();
    }
  });
};
// const setAuthorityIds = () => {
//   tableData.value &&
//     tableData.value.forEach((user) => {
//       user.authorityIds =
//         user.authorities &&
//         user.authorities.map((i) => {
//           return i.authorityId;
//         });
//     });
// };

// const authOptions = ref([]);
// const setOptions = (authData) => {
//   authOptions.value = [];
//   setAuthorityOptions(authData, authOptions.value);
// };
</script>

<style lang="scss">
.form-inline .el-input {
  --el-input-width: 220px;
}
.gva-btn-list {
  display: flex;
  justify-content: flex-end;
}
</style>
