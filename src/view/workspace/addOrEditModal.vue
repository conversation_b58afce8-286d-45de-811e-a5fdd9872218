<template>
  <el-drawer
    :model-value="props.visible"
    width="500"
    :before-close="closeAddModal"
    :show-close="false"
  >
    <template #header>
      <div class="flex justify-between items-center">
        <span class="text-lg">{{
          props.type === "add" ? "新增工作区间" : "修改工作区间"
        }}</span>
        <div>
          <el-button @click="closeAddModal">取消</el-button>
          <el-button type="primary" @click="addWorkspace"> 确定 </el-button>
        </div>
      </div>
    </template>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="项目名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入项目名称"
          type="text"
        />
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { ElMessage } from "element-plus";

// 定义 props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "add",
  },
  formData: {
    type: Object,
    default: {},
  },
});

// 定义 emits
const emit = defineEmits(["onClose", "onOk"]);

// 表单数据
const form = reactive({
  name: "",
});

// 表单引用
const formRef = ref(null);

// 表单验证规则
const rules = reactive({
  name: [{ required: true, message: "请输入", trigger: "change" }],
});

// 关闭弹窗
const closeAddModal = () => {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.name = "";
  emit("onClose");
};

// 新增
const addWorkspace = () => {
  if (!formRef.value) return;

  formRef.value.validate((valid) => {
    if (valid) {
      // 表单验证通过，提交数据
      emit("onOk", { ...form });
      closeAddModal();
    } else {
      ElMessage.error("请正确填写表单信息");
    }
  });
};

// 监听 props 变化，当编辑时设置表单数据
watch(
  () => [props.type, props.formData],
  ([type, formData]) => {
    if (type === "edit" && formData) {
      Object.assign(form, formData);
    } else if (type === "add") {
      // 新增时重置表单
      form.name = "";
    }
  },
  { immediate: true }
);
</script>
