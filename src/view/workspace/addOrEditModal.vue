<template>
  <el-dialog
    :model-value="props.visible"
    :title="props.type === 'add' ? '新增工作区间' : '修改工作区间'"
    width="500"
    :before-close="closeAddModal"
  >
    <div>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入项目名称"
            type="text"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeAddModal">取消</el-button>
        <el-button type="primary" @click="addWorkspace"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 定义 props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "add",
  },
  formData: {
    type: Object,
    default: {},
  },
});

// 定义 emits
const emit = defineEmits(["onClose", "onOk"]);

// 表单数据
const form = reactive({
  name: "",
});

// 表单引用
const formRef = ref(null);

// 表单验证规则
const rules = reactive({
  name: [{ required: true, message: "请输入", trigger: "change" }],
});

// 关闭弹窗
const closeAddModal = () => {
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  form.name = "";
  emit("onClose");
};

// 新增
const addWorkspace = () => {
  if (!formRef.value) return;

  formRef.value.validate((valid) => {
    if (valid) {
      // 表单验证通过，提交数据
      emit("onOk", { ...form });
      closeAddModal();
    } else {
      ElMessage.error("请正确填写表单信息");
    }
  });
};

onMounted(async () => {
  if (props.type === "edit") {
    formRef.value.setFieldsValue(props.formData);
  }
});
</script>
