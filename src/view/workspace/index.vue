<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="searchForm" :inline="true" :model="searchInfo">
        <el-form-item label="项目名称">
          <el-input v-model="searchInfo.name" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">
            查询
          </el-button>
          <el-button icon="refresh" @click="onReset"> 重置 </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="workspace-tools">
        <el-button type="primary" icon="plus" @click="add">新增</el-button>
      </div>

      <el-table :data="tableData" row-key="id">
        <el-table-column
          align="left"
          label="项目名称"
          min-width="150"
          prop="name"
        />
        <el-table-column
          align="left"
          label="人数"
          min-width="150"
          prop="userCount"
        />
        <el-table-column align="left" label="管理员" min-width="180">
          <template #default="scope">
            <span>{{ scope.row.admin[0] ?? "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="250" fixed="right">
          <template #default="scope">
            <el-button type="primary" link icon="edit" @click="edit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="success"
              link
              icon="edit"
              @click="openEdit(scope.row)"
              >关联人员</el-button
            >
            <el-button
              :disabled="!!scope.row.userCount"
              :type="scope.row.userCount ? 'info' : 'danger'"
              link
              icon="delete"
              @click="delWorkspace(scope.row)"
              >删除</el-button
            >

            <el-button
              type="primary"
              link
              icon="magic-stick"
              @click="resetPasswordFunc(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <!-- 新增工作区间弹窗 -->
    <AddOrEditModal
      :visible="modal.visible"
      :type="modal.type"
      :formData="modal.formData"
      @onClose="onClose"
      @onOk="onOk"
    />

    <!-- 关联用户弹窗 -->
    <RelationModal
      :visible="relationModal.visible"
      :workspaceData="relationModal.workspaceData"
      @onClose="onRelationClose"
      @onSuccess="onRelationSuccess"
    />
  </div>
</template>

<script setup>
import {
  getWorkspaceList,
  deleteWorkspace,
  addWorkspace,
  updateWorkspace,
} from "@/api/workspace";
import AddOrEditModal from "./addOrEditModal.vue";
import RelationModal from "./relationModal.vue";

import { ElMessage, ElMessageBox } from "element-plus";
import { onMounted, ref, reactive } from "vue";

defineOptions({
  name: "workspace",
});

const searchInfo = ref({
  name: "",
});

const modal = ref({
  visible: false,
  type: "add",
  formData: {},
});

const relationModal = ref({
  visible: false,
  workspaceData: {},
});

//列表查询
const onSubmit = () => {
  page.value = 1;
  getTableData();
};

//列表重置
const onReset = () => {
  searchInfo.value = {
    name: "",
  };
  getTableData();
};

const page = ref(1);
const total = ref(0);
const pageSize = ref(10);
const tableData = ref([]);
// 分页
const handleSizeChange = (val) => {
  pageSize.value = val;
  getTableData();
};

const handleCurrentChange = (val) => {
  page.value = val;
  getTableData();
};
// 查询
const getTableData = async () => {
  const { data, code } = await getWorkspaceList({
    page: page.value,
    pageSize: pageSize.value,
    ...searchInfo.value,
  });
  if (code === 0) {
    tableData.value = data.list;
    total.value = data.total;
    page.value = data.page;
    pageSize.value = data.pageSize;
  }
};
const delWorkspace = async (row) => {
  ElMessageBox.confirm("确定要删除吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const { code } = await deleteWorkspace({ tenantId: row.id });
    if (code === 0) {
      ElMessage.success("删除成功");
      await getTableData();
    }
  });
};

//新增工作区
const add = () => {
  modal.value.visible = true;
  modal.value.type = "add";
};

//编辑
const edit = (row) => {
  modal.value.visible = true;
  modal.value.type = "edit";
  modal.value.formData = row;
};

//关闭弹窗
const onClose = () => {
  modal.value.visible = false;
};

//新增提交
const onOk = async (formData) => {
  try {
    if (modal.value.type === "add") {
      const { code } = await addWorkspace(formData);
      if (code === 0) {
        ElMessage.success("新增成功");
        await getTableData();
      }
    } else {
      const { code } = await updateWorkspace({
        name: formData.name,
        tenantId: modal.value.formData.tenantId || modal.value.formData.id,
      });
      if (code === 0) {
        ElMessage.success("修改成功");
        await getTableData();
      }
    }
  } catch (error) {
    ElMessage.error("操作失败");
  }
};

// 打开关联用户弹窗
const openEdit = (row) => {
  relationModal.value.visible = true;
  relationModal.value.workspaceData = row;
};

// 关闭关联用户弹窗
const onRelationClose = () => {
  relationModal.value.visible = false;
};

// 关联用户成功
const onRelationSuccess = () => {
  relationModal.value.visible = false;
  getTableData(); // 刷新列表
};

onMounted(() => {
  getTableData();
});
</script>

<style lang="scss">
.workspace-tools {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
</style>
