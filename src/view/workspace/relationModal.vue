<template>
  <el-drawer
    :model-value="props.visible"
    title="关联用户"
    size="80%"
    :before-close="handleClose"
    :show-close="false"
  >
    <template #header>
      <div class="flex justify-between items-center w-full">
        <span class="text-lg font-medium">关联用户</span>
        <div class="flex gap-2">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </div>
    </template>

    <div class="p-4">
      <!-- 项目信息 -->
      <div class="mb-6 p-4 bg-gray-50 rounded-lg">
        <div class="flex items-center gap-4 text-sm text-gray-600">
          <span
            ><strong>项目名称：</strong>{{ workspaceInfo.name || "-" }}</span
          >
          <span><strong>管理员：</strong>{{ workspaceInfo.admin || "-" }}</span>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-6">
        <!-- 左侧：用户列表 -->
        <div>
          <!-- 搜索区域 -->
          <div class="mb-4">
            <el-form
              ref="searchFormRef"
              :model="searchForm"
              :inline="true"
              size="middle"
              class="search-form"
            >
              <el-form-item label="姓名:" prop="name">
                <el-input
                  v-model="searchForm.name"
                  placeholder="请输入"
                  clearable
                  style="width: 120px"
                />
              </el-form-item>
              <el-form-item label="工号:" prop="employeeNumber">
                <el-input
                  v-model="searchForm.employeeNumber"
                  placeholder="请输入"
                  clearable
                  style="width: 120px"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 用户表格 -->
          <div class="border rounded w-full">
            <el-table
              border
              ref="userTableRef"
              :data="userList"
              @selection-change="handleSelectionChange"
              size="small"
            >
              <el-table-column type="selection" width="40" />
              <el-table-column prop="fullName" label="姓名" />
              <el-table-column prop="employeeNumber" label="工号" />
            </el-table>

            <!-- 分页 -->
            <div class="p-2 border-t bg-gray-50">
              <el-pagination
                v-model:current-page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next"
                size="small"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>

        <!-- 右侧：已关联用户 -->
        <div>
          <div class="flex justify-between items-center mb-3">
            <span class="text-sm font-medium"
              >已关联用户（{{ selectedUsers.length }}位）</span
            >
            <el-button size="small" type="danger" @click="handleClearAll"
              >清空</el-button
            >
          </div>

          <div class="border rounded h-96 overflow-y-auto">
            <div
              v-if="selectedUsers.length === 0"
              class="flex items-center justify-center h-full text-gray-400"
            >
              暂无关联用户
            </div>
            <div v-else class="p-2">
              <div
                v-for="user in selectedUsers"
                :key="user.id"
                class="flex justify-between items-center p-2 mb-2 bg-gray-50 rounded hover:bg-gray-100"
              >
                <div class="flex items-center gap-2">
                  <span class="text-sm w-20 inline-block truncate">{{
                    user.fullName
                  }}</span>
                  <el-select
                    v-model="user.role"
                    size="small"
                    style="width: 120px"
                    @change="handleRoleChange(user)"
                    placeholder="请选择权限"
                  >
                    <el-option
                      v-for="role in roleInfo"
                      :key="role.authorityId"
                      :label="role.authorityName"
                      :value="role.authorityId"
                    />
                  </el-select>
                </div>
                <el-button
                  size="small"
                  type="text"
                  icon="Delete"
                  @click="handleRemoveUser(user)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getAuthorityList } from "@/api/authority";
import {
  linkUsers,
  getUserListByTenantId,
  getUserList as fetchUserList,
} from "@/api/workspace";

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  workspaceData: {
    type: Object,
    default: () => ({}),
  },
});

// Emits
const emit = defineEmits(["onClose", "onSuccess"]);

// 工作区信息
const workspaceInfo = ref({
  name: "",
  admin: "",
  method: "",
});

//角色信息
const roleInfo = ref([]);

// 搜索表单
const searchFormRef = ref(null);
const userTableRef = ref(null);
const searchForm = reactive({
  name: "",
  employeeNumber: "",
});

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
});

// 用户列表
const userList = ref([]);
const selectedUsers = ref([]);
const loading = ref(false);

// 获取用户列表
const getUserList = async () => {
  loading.value = true;
  try {
    const res = await fetchUserList({
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm,
    });
    if (res.code === 0) {
      // 模拟用户数据结构，因为 getAuthorityList 返回的是角色数据
      // 实际项目中应该有专门的用户列表接口
      userList.value = res.data?.list;
      pagination.total = res.data?.total || 0;

      // 恢复已选用户的勾选状态
      setTimeout(() => {
        if (userTableRef.value && selectedUsers.value.length > 0) {
          selectedUsers.value.forEach((selectedUser) => {
            const tableUser = userList.value.find(
              (u) => u.id === selectedUser.id
            );
            if (tableUser) {
              userTableRef.value.toggleRowSelection(tableUser, true);
            }
          });
        }
      }, 100);
    }
  } catch (error) {
    ElMessage.error("获取用户列表失败");
  } finally {
    loading.value = false;
  }
};

//获取角色
const getAuthority = async () => {
  try {
    const res = await getAuthorityList();
    if (res.code === 0) {
      roleInfo.value = res.data;
    }
  } catch (error) {
    ElMessage.error("获取已关联用户失败");
  }
};

// 获取已关联用户
const getLinkedUsers = async () => {
  if (!props.workspaceData?.id) return;

  try {
    const res = await getUserListByTenantId({
      tenantId: props.workspaceData.id,
    });
    if (res.code === 0) {
      selectedUsers.value =
        res.data?.list?.map((user) => ({
          ...user,
          role: user.role ? Number(user.role) : null, // 确保角色值为数字类型，如果没有角色则为null
          fullName: user.name,
        })) || [];

      // 调试信息：查看获取到的用户数据
      console.log("获取到的已关联用户:", selectedUsers.value);
      console.log("角色信息:", roleInfo.value);
    }
  } catch (error) {
    console.error("获取已关联用户失败:", error);
  }
};

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  // 获取当前选中的用户ID列表
  const selectedIds = selection.map((user) => user.id);
  console.log("selectedUsers", selection, selectedUsers.value);

  // 1. 添加新选择的用户
  selection.forEach((user) => {
    const exists = selectedUsers.value.find((u) => u.id === user.id);
    if (!exists) {
      selectedUsers.value.push({
        ...user,
      });
    }
  });

  // 2. 删除取消选择的用户
  selectedUsers.value = selectedUsers.value.filter((user) => {
    // 如果用户在当前页面的用户列表中，但没有被选中，则删除
    const isInCurrentPage = userList.value.some((u) => u.id === user.id);
    if (isInCurrentPage && !selectedIds.includes(user.id)) {
      return false; // 删除该用户
    }
    return true; // 保留该用户
  });
};

// 移除用户
const handleRemoveUser = (user) => {
  const index = selectedUsers.value.findIndex((u) => u.id === user.id);
  if (index > -1) {
    selectedUsers.value.splice(index, 1);

    // 同步取消左侧表格的勾选状态
    if (userTableRef.value) {
      // 找到对应的用户数据
      const tableUser = userList.value.find((u) => u.id === user.id);
      if (tableUser) {
        userTableRef.value.toggleRowSelection(tableUser, false);
      }
    }
  }
};

// 清空所有用户
const handleClearAll = () => {
  ElMessageBox.confirm("确定要清空所有已关联用户吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      selectedUsers.value = [];

      // 同步清空左侧表格的所有勾选状态
      if (userTableRef.value) {
        userTableRef.value.clearSelection();
      }

      ElMessage.success("已清空");
    })
    .catch(() => {});
};

// 角色变更
const handleRoleChange = (user) => {
  console.log("角色变更:", user.fullName, user.role);
};

// 搜索
const handleSearch = () => {
  pagination.page = 1;
  getUserList();
};

// 重置搜索
const handleReset = () => {
  if (searchFormRef.value) {
    searchFormRef.value.resetFields();
  }
  pagination.page = 1;
  getUserList();
};

// 分页变化
const handleSizeChange = (size) => {
  pagination.pageSize = size;
  pagination.page = 1;
  getUserList();
};

const handleCurrentChange = (page) => {
  pagination.page = page;
  getUserList();
};

// 关闭弹窗
const handleClose = () => {
  emit("onClose");
};

// 提交关联
const handleSubmit = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning("请至少选择一个用户");
    return;
  }

  try {
    // const accountIds = selectedUsers.value.map((user) => user.id);
    const res = await linkUsers({
      accountDTOList: selectedUsers.value,
      tenantId: props.workspaceData.id,
    });

    if (res.code === 0) {
      ElMessage.success("关联用户成功");
      emit("onSuccess");
      handleClose();
    } else {
      ElMessage.error(res.msg || "关联用户失败");
    }
  } catch (error) {
    ElMessage.error("关联用户失败");
  }
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  async (newVal) => {
    if (newVal) {
      // 设置工作区信息
      workspaceInfo.value = {
        name: props.workspaceData?.name || "",
        admin: props.workspaceData?.admin[0] || "",
      };

      // 重置搜索条件
      searchForm.name = "";
      searchForm.employeeNumber = "";
      pagination.page = 1;

      // 获取数据 - 先获取角色信息，再获取用户数据
      await getAuthority();
      getUserList();
      getLinkedUsers();
    }
  }
);

// 组件挂载时获取数据
onMounted(async () => {
  if (props.visible) {
    await getAuthority();
    getUserList();
    getLinkedUsers();
  }
});
</script>

<style scoped>
/* 自定义样式 */
.el-table {
  font-size: 12px;
}

.el-pagination {
  justify-content: center;
}

/* 搜索表单样式 */
.search-form {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 16px;
}

.search-form :deep(.el-form-item__label) {
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

.search-form :deep(.el-input__inner) {
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .search-form {
    padding: 12px;
  }

  .search-form :deep(.el-form-item) {
    margin-right: 8px;
  }
}
</style>
