<template>
  <div v-show="false">该页面用于对接oa-oauth2.0回调登录</div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'

defineOptions({
  name: 'LoginCallback',
})
const route = useRoute()
const userStore = useUserStore()
const ssoLogin = async() => {
  return await userStore.IamLoginIn(route.query)
}
const callback = async() => {
  if(route.query.resource === 'logout'){
    // 来源IAM退出登录跳转的页面
    window.location.href = '/'
    return
  }
  if (!route.query.code || !route.query.state) {
    ElMessage({
      type: 'error',
      message: '登录失败，授权码缺失，3秒后跳转到登录页',
      showClose: true,
    })
    // 3秒后跳转登录页
    setTimeout(() => {
      window.location.href = '/'
    }, 3000)
    return false
  }
  const flag = await ssoLogin()
  if (!flag) {
    ElMessage({
      type: 'error',
      message: '登录失败，3秒后跳转到登录页',
      showClose: true,
    })
    // 3秒后跳转登录页
    setTimeout(() => {
      window.location.href = '/'
    }, 3000)
  }
  return
}
callback()
</script>
