import CryptoJS from 'crypto-js';

const AES_KEY = '4564564564564564';
const AES_IV = '4564564564564564';
//解密
// 解密（适配 Base64 输入）
export const DecryptByAES = ({ data, key, iv }) => {
  try {
    let ciphertext = CryptoJS.enc.Base64.parse(data); // 直接解析 Base64 数据
    let keyHex = CryptoJS.enc.Utf8.parse(key ? key : AES_KEY);
    let ivHex = CryptoJS.enc.Utf8.parse(iv ? iv : AES_IV);

    let decrypt = CryptoJS.AES.decrypt(ciphertext.toString(), keyHex, {
      iv: ivHex,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.ZeroPadding,
    });

    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr;
  } catch (error) {
    return '';
  }
};

// 加密
export function EncryptByAES({ data, key, iv }) {
  let keyHex = CryptoJS.enc.Utf8.parse(key ? key : AES_KEY);
  let ivHex = CryptoJS.enc.Utf8.parse(iv ? iv : AES_IV);
  let srcs = CryptoJS.enc.Utf8.parse(data);
  let encrypted = CryptoJS.AES.encrypt(srcs, keyHex, {
    iv: ivHex,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.ZeroPadding,
  });
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext); // 改为 Base64 输出
}
