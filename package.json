{"name": "gaia-admin", "version": "0.0.1", "private": true, "scripts": {"serve": "vite --host --mode development", "build": "vite build --mode production", "build-test": "vite build --mode test", "limit-build": "npm install increase-memory-limit-fixbug cross-env -g && npm run fix-memory-limit && node ./limit && npm run build", "preview": "vite preview", "fix-memory-limit": "cross-env LIMIT=4096 increase-memory-limit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@form-create/designer": "^3.2.6", "@form-create/element-ui": "^3.2.10", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.2", "@vueuse/core": "^11.0.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ace-builds": "^1.36.4", "axios": "^1.7.7", "chokidar": "^4.0.0", "core-js": "^3.38.1", "crypto-js": "^4.2.0", "default-passive-events": "^2.0.0", "echarts": "5.5.1", "element-plus": "^2.8.5", "highlight.js": "^11.10.0", "js-cookie": "^3.0.5", "marked": "14.1.1", "marked-highlight": "^2.1.4", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.2.2", "qs": "^6.13.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.3", "spark-md5": "^3.0.2", "tailwindcss": "^3.4.10", "vform3-builds": "^3.0.10", "vite-auto-import-svg": "^1.1.0", "vue": "^3.5.7", "vue-echarts": "^7.0.3", "vue-router": "^4.4.3", "vue3-ace-editor": "^2.2.4", "vuedraggable": "^4.1.0"}, "devDependencies": {"@babel/eslint-parser": "^7.25.1", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue": "^5.1.3", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/compiler-sfc": "^3.5.1", "babel-plugin-import": "^1.13.8", "chalk": "^5.3.0", "dotenv": "^16.4.5", "eslint": "^9.9.1", "eslint-plugin-vue": "^9.28.0", "sass": "^1.78.0", "terser": "^5.31.6", "vite": "^5.4.3", "vite-plugin-banner": "^0.8.0", "vite-plugin-importer": "^0.2.5", "vite-plugin-vue-devtools": "^7.4.4"}}